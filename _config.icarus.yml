# Version of the configuration file
version: 5.1.0
# Icarus theme variant, can be "default" or "cyberpunk"
variant: default
# Path or URL to the website's logo
logo: https://r2.ojkapps.com/img/logo.svg
# Page metadata configurations
head:
    # URL or path to the website's icon
    favicon: https://r2.ojkapps.com/img/favicon.svg
    # Web application manifests configuration
    # https://developer.mozilla.org/en-US/docs/Web/Manifest
    manifest:
        # Name of the web application (default to the site title)
        name: 
        # The displayed name of the web application
        # when there is not enough space to display full name
        short_name: Pinjaman OJK
        # The start URL of the web application
        start_url: /
        # The default theme color for the application
        theme_color: '#A11914'
        # A placeholder background color for the application page to display
        # before its stylesheet is loaded
        background_color: '#FFFFFF'
        # The preferred display mode for the website
        display: standalone
        # Image files that can serve as application icons for different contexts
        icons:
            -
                # The path to the image file
                src: https://r2.ojkapps.com/img/logo.svg
                # A string containing space-separated image dimensions
                sizes: '512x512 192x192 144x144 96x96 72x72 48x48 32x32 16x16'  
                # A hint as to the media type of the image
                type: image/svg+xml
    # Open Graph metadata
    # https://hexo.io/docs/helpers.html#open-graph
    open_graph:
        # Page title (og:title) (optional)
        # You should leave this blank for most of the time
        title: 
        # Page type (og:type) (optional)
        # You should leave this blank for most of the time
        type: blog
        # Page URL (og:url) (optional)
        # You should leave this blank for most of the time
        url: 
        # Page cover (og:image) (optional)
        # You should leave this blank for most of the time
        image: 
        # Site name (og:site_name) (optional)
        # You should leave this blank for most of the time
        site_name: 
        # Page author (article:author) (optional)
        # You should leave this blank for most of the time
        author: 
        # Page description (og:description) (optional)
        # You should leave this blank for most of the time
        description: 
        # Twitter card type (twitter:card)
        twitter_card: 
        # Twitter ID (twitter:creator)
        twitter_id: 
        # Twitter Site (twitter:site)
        twitter_site: 
        # Google+ profile link (deprecated)
        google_plus: 
        # Facebook admin ID
        fb_admins: 
        # Facebook App ID
        fb_app_id: 
    # Structured data of the page
    # https://developers.google.com/search/docs/guides/intro-structured-data
    structured_data:
        # Page title (optional)
        # You should leave this blank for most of the time
        title: 
        # Page description (optional)
        # You should leave this blank for most of the time
        description: 
        # Page URL (optional)
        # You should leave this blank for most of the time
        url: 
        # Page author (article:author) (optional)
        # You should leave this blank for most of the time
        author: 
        # Page publisher (optional)
        # You should leave this blank for most of the time
        publisher: 
        # Page publisher logo (optional)
        # You should leave this blank for most of the time
        publisher_logo: 
        # Page images (optional)
        # You should leave this blank for most of the time
        image: 
    # Additional HTML meta tags in an array
    meta:
        # Meta tag specified in <attribute>=<value> style
        # E.g., name=theme-color;content=#123456 => <meta name="theme-color" content="#123456">
        - ''
    # URL or path to the website's RSS atom.xml
    rss: /atom.xml
# Page top navigation bar configurations
navbar:
    # Navigation menu items
    menu:
        Beranda: /
        Arsip: /archives
        Kategori: /categories
        Tags: /tags
        Privasi: /privacy
    # Links to be shown on the right of the navigation bar
    # links:
    #     Download on GitHub:
    #         icon: fab fa-github
    #         url: https://github.com/ppoffice/hexo-theme-icarus
# Page footer configurations
footer:
    # Copyright text
    copyright: ''
    # Links to be shown on the right of the footer section
    links:
        Creative Commons:
            icon: fab fa-creative-commons
            url: https://creativecommons.org/
        Attribution 4.0 International:
            icon: fab fa-creative-commons-by
            url: https://creativecommons.org/licenses/by/4.0/
        Download on GitHub:
            icon: fab fa-github
            url: https://github.com/ppoffice/hexo-theme-icarus
# Article related configurations
article:
    # Code highlight settings
    highlight:
        # Code highlight themes
        # https://github.com/highlightjs/highlight.js/tree/master/src/styles
        theme: atom-one-light
        # Show copy code button
        clipboard: true
        # Default folding status of the code blocks. Can be "", "folded", "unfolded"
        fold: unfolded
    # Whether to show estimated article reading time
    readtime: false
    # Whether to show updated time. For "auto", shows article update time only when page.updated is set and it is different from page.date
    update_time: true
    # Article licensing block
    licenses:
        Creative Commons:
            icon: fab fa-creative-commons
            url: https://creativecommons.org/
        Attribution:
            icon: fab fa-creative-commons-by
            url: https://creativecommons.org/licenses/by/4.0/
        Noncommercial:
            icon: fab fa-creative-commons-nc
            url: https://creativecommons.org/licenses/by-nc/4.0/
# Search plugin configurations
# https://ppoffice.github.io/hexo-theme-icarus/categories/Plugins/Search/
search:
    type: insight
    # Whether to include pages in the search results
    index_pages: true
# Comment plugin configurations
# https://ppoffice.github.io/hexo-theme-icarus/categories/Plugins/Comment/
# comment:
#     type: disqus
#     # Disqus shortname
#     shortname: ''
# Donate plugin configurations
# https://ppoffice.github.io/hexo-theme-icarus/categories/Plugins/Donation/
# donates:
#     # "Afdian.net" donate button configurations
#     -
#         type: afdian
#         # URL to the "Afdian.net" personal page
#         url: ''
#     # Alipay donate button configurations
#     -
#         type: alipay
#         # Alipay qrcode image URL
#         qrcode: ''
#     # "Buy me a coffee" donate button configurations
#     -
#         type: buymeacoffee
#         # URL to the "Buy me a coffee" page
#         url: ''
#     # Patreon donate button configurations
#     -
#         type: patreon
#         # URL to the Patreon page
#         url: ''
#     # Paypal donate button configurations
#     -
#         type: paypal
#         # Paypal business ID or email address
#         business: ''
#         # Currency code
#         currency_code: USD
#     # Wechat donate button configurations
#     -
#         type: wechat
#         # Wechat qrcode image URL
#         qrcode: ''
# Share plugin configurations
# https://ppoffice.github.io/hexo-theme-icarus/categories/Plugins/Share/
share:
    type: sharethis
    # URL to the ShareThis share plugin script
    install_url: https://platform-api.sharethis.com/js/sharethis.js#property=67e25c136aa9ea001960dd6f&product=inline-share-buttons
# Sidebar configurations.
# Please be noted that a sidebar is only visible when it has at least one widget
sidebar:
    # Left sidebar configurations
    left:
        # Whether the sidebar sticks to the top when page scrolls
        sticky: false
    # Right sidebar configurations
    right:
        # Whether the sidebar sticks to the top when page scrolls
        sticky: false
# Sidebar widget configurations
# http://ppoffice.github.io/hexo-theme-icarus/categories/Widgets/
widgets:
    # Profile widget configurations
    -
        # Where should the widget be placed, left sidebar or right sidebar
        position: left
        type: profile
        # # Author name
        # author: Your name
        # # Author title
        # author_title: Your title
        # # Author's current location
        # location: Your location
        # URL or path to the avatar image
        avatar: https://r2.ojkapps.com/img/favicon.svg
        # # Whether show the rounded avatar image
        # avatar_rounded: false
        # # Email address for the Gravatar
        # gravatar: 
        # # URL or path for the follow button
        # follow_link: https://github.com/ppoffice
        # Links to be shown on the bottom of the profile widget
        social_links:
            # Github:
            #     icon: fab fa-github
            #     url: https://github.com/ppoffice
            # Facebook:
            #     icon: fab fa-facebook
            #     url: https://facebook.com
            # Twitter:
            #     icon: fab fa-twitter
            #     url: https://twitter.com
            # Dribbble:
            #     icon: fab fa-dribbble
            #     url: https://dribbble.com
            RSS:
                icon: fas fa-rss
                url: /atom.xml
    # Table of contents widget configurations
    -
        # Where should the widget be placed, left sidebar or right sidebar
        position: left
        type: toc
        # Whether to show the index of each heading
        index: true
        # Whether to collapse sub-headings when they are out-of-view
        collapsed: true
        # Maximum level of headings to show (1-6)
        depth: 3
    # Recommendation links widget configurations
    -
        # Where should the widget be placed, left sidebar or right sidebar
        position: left
        type: links
        # Names and URLs of the sites
        links:
            OJK: https://ojk.go.id/en/berita-dan-kegiatan/pengumuman/Pages/The-List-of-Licensed-Fintech-Lending-Companies-at-The-Financial-Services-Authority.aspx
            AFPI: https://www.afpi.or.id/
    # Categories widget configurations
    -
        # Where should the widget be placed, left sidebar or right sidebar
        position: left
        type: categories
    # Recent posts widget configurations
    -
        # Where should the widget be placed, left sidebar or right sidebar
        position: left
        type: recent_posts
    # Archives widget configurations
    -
        # Where should the widget be placed, left sidebar or right sidebar
        position: left
        type: archives
    # Tags widget configurations
    -
        # Where should the widget be placed, left sidebar or right sidebar
        position: left
        type: tags
        # How to order tags. For example 'name' to order by name in ascending order, and '-length' to order by number of posts in each tags in descending order
        order_by: name
        # Amount of tags to show. Will show all if not set.
        amount: 
        # Whether to show tags count, i.e. number of posts in the tag.
        show_count: true
    # # Google FeedBurner email subscription widget configurations
    # -
    #     # Where should the widget be placed, left sidebar or right sidebar
    #     position: left
    #     type: subscribe_email
    #     # Hint text under the email input
    #     description: "Langganan pembaruan email kami dan dapatkan informasi terbaru serta penawaran menarik! Masukkan alamat email Anda, dan kami akan mengirimkan pembaruan secara berkala."  
    #     # Feedburner ID
    #     feedburner_id: ojkapps/qs3iwjxjejg
    # Google AdSense unit configurations
    # -
    #     # Where should the widget be placed, left sidebar or right sidebar
    #     position: left
    #     type: adsense
    #     # AdSense client ID
    #     client_id: ''
    #     # AdSense AD unit ID
    #     slot_id: ''
    # Follow.it email subscription widget configurations
    -
        # Where should the widget be placed, left sidebar or right sidebar
        position: left
        type: followit
        # Hint text under the email input
        description: "Selamat datang di OJKApps! Blog ini menyajikan informasi terkini seputar keuangan, aplikasi digital, dan tips berharga untuk membantu Anda mengelola keuangan secara lebih efisien. Bergabunglah dengan kami untuk mendapatkan pembaruan dan penawaran menarik!"  
        # Subscription form action URL
        action_url: https://api.follow.it/subscription-form/bytrRlBHd0V3cWdiVE9zeUYrY3BtYlZmOXl4Yzg0S0lrUkZiWVZOWUVQNDBtRHJuTlpsMWp1aTFUMzRJUzNVQ1k1QWNUVkRkbVBvQkhsRE5JK29pUDh3Q3pQMDhtQzRaU08wUzVBTFZaQ0lVUmk5SFFITGdmTzRJZ1dHbHB6UHR8N3JJck5oc1hiRDg4OUM5d054dko5NjFWQUxwZHZsMUt2RUd1RlFuM053RT0=/8
        # Feed claiming verification code
        verification_code: AA3x0PNvWXNPNwjy1z5q
# Plugin configurations
# https://ppoffice.github.io/hexo-theme-icarus/categories/Plugins/
plugins:
    # Enable page startup animations
    animejs: true
    # Show the "back to top" button
    back_to_top: true
    # Baidu Analytics plugin settings
    # https://tongji.baidu.com
    baidu_analytics:
        # Baidu Analytics tracking ID
        tracking_id: 
    # Bing Webmaster Tools plugin settings
    # https://www.bing.com/toolbox/webmaster/
    bing_webmaster:
        # Bing Webmaster Tools tracking ID in the <meta> tag
        tracking_id: 
    # BuSuanZi site/page view counter
    # https://busuanzi.ibruce.info
    busuanzi: false
    # CNZZ statistics
    # https://www.umeng.com/web
    cnzz:
        # CNZZ tracker id
        id: 
        # CNZZ website id
        web_id: 
    # Alerting users about the use of cookies
    # https://www.osano.com/cookieconsent/
    cookie_consent:
        # The compliance type. Can be "info", "opt-in", or "opt-out"
        type: info
        # Theme of the popup. Can be "block", "edgeless", or "classic"
        theme: edgeless
        # Whether the popup should stay static regardless of the page scrolls
        static: false
        # Where on the screen the consent popup should display
        position: bottom-left
        # URL to your site's cookie policy
        policyLink: /privacy
    # Enable the lightGallery and Justified Gallery plugins
    gallery: true
    # Google Analytics plugin settings
    # https://analytics.google.com
    google_analytics:
        # Google Analytics tracking ID
        tracking_id: G-GQKPKRM37P
    # Hotjar user feedback plugin
    # https://www.hotjar.com/
    hotjar:
        # Hotjar site id
        site_id: 
    # Enable the KaTeX math typesetting support
    # https://katex.org/
    katex: false
    # Enable the MathJax math typesetting support
    # https://www.mathjax.org/
    mathjax: false
    # Enable the Outdated Browser plugin
    # http://outdatedbrowser.com/
    outdated_browser: false
    # Enable PJAX
    pjax: true
    # Show a progress bar at top of the page on page loading
    progressbar: true
    # Statcounter statistics
    # https://statcounter.com/
    statcounter:
        # Statcounter project id
        project: 
        # Statcounter project security code
        security: 
    # Twitter conversion tracking plugin settings
    # https://business.twitter.com/en/help/campaign-measurement-and-analytics/conversion-tracking-for-websites.html
    twitter_conversion_tracking:
        # Twitter Pixel ID
        pixel_id: 
# CDN provider settings
# https://ppoffice.github.io/hexo-theme-icarus/Configuration/Theme/speed-up-your-site-with-custom-cdn/
providers:
    # Name or URL template of the JavaScript and/or stylesheet CDN provider
    cdn: jsdelivr
    # Name or URL template of the webfont CDN provider
    fontcdn: google
    # Name or URL of the fontawesome icon font CDN provider
    iconcdn: fontawesome

