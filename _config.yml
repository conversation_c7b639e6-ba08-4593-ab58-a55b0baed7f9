# Hexo Configuration
## Docs: https://hexo.io/docs/configuration.html
## Source: https://github.com/hexojs/hexo/

# Site
title: Pinjaman OJK
subtitle: Menemukan Aplikasi Pinjaman Resmi yang OJK Terdaftar
description: OJKApps.com adalah sumber terpercaya untuk menemukan aplikasi pinjaman yang telah terdaftar dan diawasi oleh OJK. Dengan informasi terkini dan ulasan pengguna, kami membantu Anda memilih solusi pinjaman yang aman dan sesuai kebutuhan Anda.
keywords: OJK, Aplikasi Pinjaman, Pinjaman Online, Pinjaman Resmi, Aplikasi Terpercaya, <PERSON><PERSON>it OJK, Pinjaman Aman
author: <PERSON>
language: id
timezone: Asia/Jakarta

# URL
## Set your site url here. For example, if you use GitHub Page, set url as 'https://username.github.io/project'
url: https://ojkapps.com
permalink: :year/:month/:day/:title/
permalink_defaults:
pretty_urls:
  trailing_index: true # Set to false to remove trailing 'index.html' from permalinks
  trailing_html: true # Set to false to remove trailing '.html' from permalinks

# Directory
source_dir: source
public_dir: public
tag_dir: tags
archive_dir: archives
category_dir: categories
code_dir: downloads/code
i18n_dir: :lang
skip_render:

# Writing
new_post_name: :year-:month-:day-:title.md # File name of new posts
default_layout: post
titlecase: false # Transform title into titlecase
external_link:
  enable: true # Open external links in new tab
  field: site # Apply to the whole site
  exclude: ''
filename_case: 0
render_drafts: false
post_asset_folder: false
relative_link: false
future: true
syntax_highlighter: highlight.js
highlight:
  line_number: true
  auto_detect: false
  tab_replace: ''
  wrap: true
  hljs: false
prismjs:
  preprocess: true
  line_number: true
  tab_replace: ''

# Home page setting
# path: Root path for your blogs index page. (default = '')
# per_page: Posts displayed per page. (0 = disable pagination)
# order_by: Posts order. (Order by date descending by default)
index_generator:
  path: ''
  per_page: 10
  order_by: -date

# Category & Tag
default_category: uncategorized
category_map:
tag_map:

# Metadata elements
## https://developer.mozilla.org/en-US/docs/Web/HTML/Element/meta
meta_generator: true

# Date / Time format
## Hexo uses Moment.js to parse and display date
## You can customize the date format as defined in
## http://momentjs.com/docs/#/displaying/format/
date_format: YYYY-MM-DD
time_format: HH:mm:ss
## updated_option supports 'mtime', 'date', 'empty'
updated_option: 'mtime'

# Pagination
## Set per_page to 0 to disable pagination
per_page: 10
pagination_dir: page

# Include / Exclude file(s)
## include:/exclude: options only apply to the 'source/' folder
include:
exclude:
ignore:

# Extensions
## Plugins: https://hexo.io/plugins/
## Themes: https://hexo.io/themes/
theme: icarus

plugins:
  hexo-generator-feed

#Feed Atom
feed:
  type: atom
  path: atom.xml
  limit: 20

# Deployment
## Docs: https://hexo.io/docs/one-command-deployment
deploy:
  type: git
  repo: https://github.com/zhoujunjie221/ojkapps-web.git
  branch: main
  message: 'Site updated: {{ now(''yyyy-MM-dd HH:mm:ss'') }}'

# Sitemap
sitemap:  
  path: sitemap.xml  
  hostname: https://ojkapps.com  
  tags: false
  categories: false

# Robot
robots:
  user_agent: "*"   # 如果要写通配符，请务必要加上双引号，不然解析会报错
  allow:
    - /
  disallow:
    - /js/
    - /css/
    - /images/
    - /archives/
    - /tags/
    - /categories/
  sitemaps:
    - https://ojkapps.com/sitemap.xml

# Nofollow
# enable - 是否启用插件，默认值为 true
# field - 插件的处理范围，默认值为 site，可选 post 或 site
# post - 仅处理文章内容
# site - 处理全站所有页面
# exclude - 域名白名单，不同的子域名视为不同的域名（如 www）
# exclude1.com不包括 www.exclude1.com 或 en.exclude1.com
nofollow:
  enable: true
  field: post

# 自动生成front-matter字段的hexo插件
# https://github.com/RayKr/hexo-auto-front-matter
auto_front_matter:
  enable: true
  # 是否保留原来的front-matter顺序
  # 'order'是一个列表，默认为空。在这种情况下，更新的front-matter字段将顺序保持在原始文本中。
  # 当'order'不为空时，front-matter将按以下顺序排序，支持少于或多于原文front-matter的真实数量。
  order:
    # - title
    # - date
    # - categories
    # - tags
    # - cover
    # - description

  # 从文件名自动添加标题
  auto_title:
    enable: true
    # 两种模式：仅当标题为空时使用“模式”控制写入，或始终覆盖。
    # 1: 仅当标题为空自动添加（默认）
    # 2: 始终使用文件名覆盖标题
    mode: 1

  # 如果为空，则自动添加现在的日期
  auto_date:
    enable: true
  
  # 逻辑与 auto_category 相同
  # https://github.com/xu-song/hexo-auto-category
  auto_categories:
    enable: true
    multiple: false
    depth:

  # 当标签为空时自动将类别列表附加到标签
  auto_tags:
    enable: true

  # 从"per_category"列表中自动添加封面图片路径
  auto_cover:
    enable: true
    # 定义每个类别的封面图片路径
    # 当存在嵌套类别时，它会选择最深的类别匹配。
    per_category:
      # - Hexo: /img/xxxx.png

mermaid:
    enable: true
    theme: default